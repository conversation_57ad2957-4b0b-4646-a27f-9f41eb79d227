import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

// Types for token management (kept for backward compatibility)
interface TokenData {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}

// API base configuration
const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:3001/api";

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Important for HTTP-only cookies
});

// Token management utilities for HTTP-only cookies
class TokenManager {
  // For HTTP-only cookies, we don't store tokens in localStorage
  // Instead, we rely on the browser to automatically send cookies

  static setTokens(_tokenData: TokenData): void {
    // With HTTP-only cookies, tokens are set by the server via Set-Cookie headers
    // We don't need to store them in localStorage anymore
    // This method is kept for backward compatibility but does nothing
  }

  static getAccessToken(): string | null {
    // With HTTP-only cookies, we can't access the token from JavaScript
    // The browser automatically includes it in requests
    return null;
  }

  static getRefreshToken(): string | null {
    // With HTTP-only cookies, we can't access the refresh token from JavaScript
    return null;
  }

  static getExpiresIn(): string | null {
    // We can't determine expiration from HTTP-only cookies in JavaScript
    return null;
  }

  static clearTokens(): void {
    // With HTTP-only cookies, tokens are cleared by the server via logout endpoint
    // This method is kept for backward compatibility but does nothing
  }

  static isTokenExpired(): boolean {
    // With HTTP-only cookies, we can't check expiration from JavaScript
    // We rely on the server to handle token validation and refresh
    // Return false to let the server handle authentication
    return false;
  }

  // New method to check if we might be authenticated
  // This is a best-effort check since we can't access HTTP-only cookies
  static isLikelyAuthenticated(): boolean {
    // We can't check the actual token, so we'll rely on the server
    // to respond with 401 if the token is invalid/expired
    return true; // Assume authenticated, let server validate
  }
}

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });

  failedQueue = [];
};

// Request interceptor - no need to add auth token for HTTP-only cookies
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // With HTTP-only cookies, the browser automatically includes the authentication cookie
    // No need to manually add Authorization header
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
    };

    // If error is 401 and we haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(() => {
            // No need to set Authorization header with HTTP-only cookies
            return apiClient(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      // With HTTP-only cookies, we don't need to check for refresh token
      // The browser will automatically send the refresh token cookie

      try {
        // Attempt to refresh the token using HTTP-only cookies
        // The refresh token cookie will be automatically sent
        const response = await axios.post(
          `${API_BASE_URL}/auth/refresh`,
          {},
          {
            withCredentials: true, // Ensure cookies are sent
          }
        );

        if (response.data.success) {
          // Token refresh successful, new token is set in HTTP-only cookie
          // Process the queue
          processQueue(null, "refreshed");

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          throw new Error("Token refresh failed");
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        processQueue(refreshError, null);
        window.location.href = "/login";
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export { TokenManager };
export type { TokenData };
