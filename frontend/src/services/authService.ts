import { api<PERSON><PERSON>, TokenManager } from '@/lib/api';
import type {
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  ProfileResponse,
  ChangePasswordRequest,
  UpdateProfileRequest,
} from 'bookmarked-types';

export class AuthService {
  /**
   * Register a new user
   */
  static async register(data: RegisterRequest): Promise<RegisterResponse> {
    try {
      const response = await apiClient.post<RegisterResponse>('/auth/register', data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Login user and store tokens
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/login', data);
      
      if (response.data.success && response.data.data) {
        // Store tokens in localStorage and HTTP-only cookies will be set by backend
        TokenManager.setTokens({
          accessToken: response.data.data.token,
          refreshToken: response.data.data.token, // Backend should provide separate refresh token
          expiresIn: response.data.data.expiresIn,
        });
      }
      
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Logout user and clear tokens
   */
  static async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate tokens on server
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, clear local tokens
      console.warn('Logout request failed, but clearing local tokens:', error);
    } finally {
      // Always clear local tokens
      TokenManager.clearTokens();
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(): Promise<{ accessToken: string; refreshToken: string; expiresIn: string }> {
    try {
      const refreshToken = TokenManager.getRefreshToken();
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiClient.post('/auth/refresh', {
        refreshToken,
      });

      const { accessToken, refreshToken: newRefreshToken, expiresIn } = response.data.data;
      
      TokenManager.setTokens({
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn,
      });

      return { accessToken, refreshToken: newRefreshToken, expiresIn };
    } catch (error: any) {
      TokenManager.clearTokens();
      throw this.handleAuthError(error);
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<ProfileResponse> {
    try {
      const response = await apiClient.get<ProfileResponse>('/auth/profile');
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(data: UpdateProfileRequest): Promise<ProfileResponse> {
    try {
      const response = await apiClient.put<ProfileResponse>('/auth/profile', data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Change user password
   */
  static async changePassword(data: ChangePasswordRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post('/auth/change-password', data);
      return response.data;
    } catch (error: any) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = TokenManager.getAccessToken();
    return !!token && !TokenManager.isTokenExpired();
  }

  /**
   * Get current access token
   */
  static getAccessToken(): string | null {
    return TokenManager.getAccessToken();
  }

  /**
   * Handle authentication errors consistently
   */
  private static handleAuthError(error: any): Error {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message);
    }
    
    if (error.response?.status === 401) {
      return new Error('Invalid credentials');
    }
    
    if (error.response?.status === 403) {
      return new Error('Access forbidden');
    }
    
    if (error.response?.status === 422) {
      return new Error('Validation failed');
    }
    
    if (error.message) {
      return new Error(error.message);
    }
    
    return new Error('An unexpected error occurred');
  }
}

export default AuthService;
