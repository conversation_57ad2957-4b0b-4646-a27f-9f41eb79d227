import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import AuthService from '@/services/authService';
import type { UserDocument } from 'bookmarked-types';

// Auth context types
interface AuthContextType {
  user: UserDocument | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (data: { email: string; password: string; firstName: string; lastName: string }) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const queryClient = useQueryClient();

  // Query to fetch user profile
  const {
    data: userProfile,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser,
  } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: async () => {
      const response = await AuthService.getProfile();
      return response.data.user;
    },
    enabled: isAuthenticated && AuthService.isAuthenticated(),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = () => {
      const isAuth = AuthService.isAuthenticated();
      setIsAuthenticated(isAuth);
      setIsInitialized(true);
    };

    initializeAuth();
  }, []);

  // Update auth state when user query changes
  useEffect(() => {
    if (userError && isAuthenticated) {
      // If user query fails and we thought we were authenticated, log out
      handleLogout();
    }
  }, [userError, isAuthenticated]);

  // Login function
  const login = async (email: string, password: string): Promise<void> => {
    try {
      const response = await AuthService.login({ email, password });
      
      if (response.success) {
        setIsAuthenticated(true);
        // Invalidate and refetch user data
        await queryClient.invalidateQueries({ queryKey: ['user'] });
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      setIsAuthenticated(false);
      throw error;
    }
  };

  // Register function
  const register = async (data: { 
    email: string; 
    password: string; 
    firstName: string; 
    lastName: string; 
  }): Promise<void> => {
    try {
      const response = await AuthService.register(data);
      
      if (!response.success) {
        throw new Error(response.message || 'Registration failed');
      }
      
      // Registration successful, but don't auto-login
      // User should be redirected to login page
    } catch (error: any) {
      throw error;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    await handleLogout();
  };

  // Internal logout handler
  const handleLogout = async (): Promise<void> => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.warn('Logout service call failed:', error);
    } finally {
      setIsAuthenticated(false);
      // Clear all queries
      queryClient.clear();
    }
  };

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    if (isAuthenticated) {
      await refetchUser();
    }
  };

  // Determine loading state
  const isLoading = !isInitialized || (isAuthenticated && isUserLoading);

  // Context value
  const value: AuthContextType = {
    user: userProfile || null,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
