// Simple test script to verify HTTP-only cookie authentication
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// Create axios instance with cookie support
const client = axios.create({
  baseURL: API_BASE,
  withCredentials: true,
  timeout: 10000,
});

async function testAuthentication() {
  console.log('🧪 Testing HTTP-only cookie authentication...\n');

  try {
    // Test 1: Login
    console.log('1️⃣ Testing login...');
    const loginResponse = await client.post('/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('✅ Login successful!');
    console.log('Response:', loginResponse.data);
    console.log('Set-Cookie headers:', loginResponse.headers['set-cookie']);
    console.log('');

    // Test 2: Get profile (should work with cookies)
    console.log('2️⃣ Testing profile access...');
    const profileResponse = await client.get('/auth/profile');
    
    console.log('✅ Profile access successful!');
    console.log('Profile data:', profileResponse.data);
    console.log('');

    // Test 3: Logout
    console.log('3️⃣ Testing logout...');
    const logoutResponse = await client.post('/auth/logout');
    
    console.log('✅ Logout successful!');
    console.log('Response:', logoutResponse.data);
    console.log('');

    // Test 4: Try to access profile after logout (should fail)
    console.log('4️⃣ Testing profile access after logout...');
    try {
      await client.get('/auth/profile');
      console.log('❌ Profile access should have failed after logout!');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Profile access correctly denied after logout!');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 429) {
      console.log('⏰ Rate limited. Please wait and try again.');
    }
  }
}

// Run the test
testAuthentication();
