"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const bookmarked_types_1 = require("bookmarked-types");
const validation_1 = require("../utils/validation");
const auth_middleware_1 = require("../middleware/auth.middleware");
const user_controller_1 = require("../controllers/user.controller");
const router = (0, express_1.Router)();
router.put("/update-profile", auth_middleware_1.authenticate, (0, validation_1.validate)({ body: bookmarked_types_1.UpdateProfileSchema }), user_controller_1.updateProfile);
router.post("/change-password", auth_middleware_1.authenticate, (0, validation_1.validate)({ body: bookmarked_types_1.ChangePasswordSchema }), user_controller_1.changePassword);
router.post("/deactivate", auth_middleware_1.authenticate, user_controller_1.deactivateAccount);
exports.default = router;
//# sourceMappingURL=user.routes.js.map