"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = exports.UserService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const bookmarked_types_1 = require("bookmarked-types");
const User_1 = require("../models/User");
const password_1 = require("../utils/password");
const error_middleware_1 = require("../middleware/error.middleware");
class UserService {
    async getUserProfile(userId) {
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            throw new error_middleware_1.ApiError("Invalid user ID", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        const user = await User_1.UserModel.findById(userId);
        if (!user) {
            throw new error_middleware_1.ApiError("User not found", bookmarked_types_1.HttpStatus.NOT_FOUND, bookmarked_types_1.ErrorCodes.NOT_FOUND);
        }
        return user.toSafeObject();
    }
    async updateUserProfile(userId, data) {
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            throw new error_middleware_1.ApiError("Invalid user ID", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        const user = await User_1.UserModel.findById(userId);
        if (!user) {
            throw new error_middleware_1.ApiError("User not found", bookmarked_types_1.HttpStatus.NOT_FOUND, bookmarked_types_1.ErrorCodes.NOT_FOUND);
        }
        if (data.firstName !== undefined) {
            user.firstName = data.firstName;
        }
        if (data.lastName !== undefined) {
            user.lastName = data.lastName;
        }
        if (data.preferences) {
            if (data.preferences.defaultView !== undefined) {
                user.preferences.defaultView = data.preferences.defaultView;
            }
            if (data.preferences.itemsPerPage !== undefined) {
                user.preferences.itemsPerPage = data.preferences.itemsPerPage;
            }
            if (data.preferences.theme !== undefined) {
                user.preferences.theme = data.preferences.theme;
            }
            if (data.preferences.language !== undefined) {
                user.preferences.language = data.preferences.language;
            }
            if (data.preferences.timezone !== undefined) {
                user.preferences.timezone = data.preferences.timezone;
            }
        }
        await user.save();
        return user.toSafeObject();
    }
    async changeUserPassword(userId, data) {
        const { currentPassword, newPassword, confirmPassword } = data;
        if (newPassword !== confirmPassword) {
            throw new error_middleware_1.ApiError("New password and confirmation do not match", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            throw new error_middleware_1.ApiError("Invalid user ID", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        const user = await User_1.UserModel.findById(userId).select("+password");
        if (!user) {
            throw new error_middleware_1.ApiError("User not found", bookmarked_types_1.HttpStatus.NOT_FOUND, bookmarked_types_1.ErrorCodes.NOT_FOUND);
        }
        const isCurrentPasswordValid = await (0, password_1.comparePassword)(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new error_middleware_1.ApiError("Current password is incorrect", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
        }
        const hashedNewPassword = await (0, password_1.hashPassword)(newPassword);
        user.password = hashedNewPassword;
        await user.save();
    }
    async getUserByEmail(email) {
        return User_1.UserModel.findByEmail(email);
    }
    async getUserById(userId) {
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return null;
        }
        return User_1.UserModel.findById(userId);
    }
    async deactivateUser(userId) {
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            throw new error_middleware_1.ApiError("Invalid user ID", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        const user = await User_1.UserModel.findById(userId);
        if (!user) {
            throw new error_middleware_1.ApiError("User not found", bookmarked_types_1.HttpStatus.NOT_FOUND, bookmarked_types_1.ErrorCodes.NOT_FOUND);
        }
        user.isActive = false;
        await user.save();
    }
    async reactivateUser(userId) {
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            throw new error_middleware_1.ApiError("Invalid user ID", bookmarked_types_1.HttpStatus.BAD_REQUEST, bookmarked_types_1.ErrorCodes.VALIDATION_ERROR);
        }
        const user = await User_1.UserModel.findById(userId);
        if (!user) {
            throw new error_middleware_1.ApiError("User not found", bookmarked_types_1.HttpStatus.NOT_FOUND, bookmarked_types_1.ErrorCodes.NOT_FOUND);
        }
        user.isActive = true;
        await user.save();
    }
}
exports.UserService = UserService;
exports.userService = new UserService();
//# sourceMappingURL=user.service.js.map