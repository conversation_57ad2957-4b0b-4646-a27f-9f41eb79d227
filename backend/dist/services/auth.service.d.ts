import { RegisterRequest, LoginRequest, UserDocument } from "bookmarked-types";
export interface AuthResult {
    user: UserDocument;
    token: string;
    expiresIn: string;
}
export declare class AuthService {
    registerUser(data: RegisterRequest): Promise<AuthResult>;
    loginUser(data: LoginRequest): Promise<AuthResult>;
}
export declare const authService: AuthService;
//# sourceMappingURL=auth.service.d.ts.map