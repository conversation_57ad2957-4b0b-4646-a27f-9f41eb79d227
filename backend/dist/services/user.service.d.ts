import { UpdateProfileRequest, ChangePasswordRequest, UserDocument } from "bookmarked-types";
import { UserDoc } from "../models/User";
export declare class UserService {
    getUserProfile(userId: string): Promise<UserDocument>;
    updateUserProfile(userId: string, data: UpdateProfileRequest): Promise<UserDocument>;
    changeUserPassword(userId: string, data: ChangePasswordRequest): Promise<void>;
    getUserByEmail(email: string): Promise<UserDoc | null>;
    getUserById(userId: string): Promise<UserDoc | null>;
    deactivateUser(userId: string): Promise<void>;
    reactivateUser(userId: string): Promise<void>;
}
export declare const userService: UserService;
//# sourceMappingURL=user.service.d.ts.map