"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authService = exports.AuthService = void 0;
const bookmarked_types_1 = require("bookmarked-types");
const User_1 = require("../models/User");
const password_1 = require("../utils/password");
const jwt_1 = require("../utils/jwt");
const error_middleware_1 = require("../middleware/error.middleware");
class AuthService {
    async registerUser(data) {
        const { email, password, firstName, lastName } = data;
        const existingUser = await User_1.UserModel.findByEmail(email);
        if (existingUser) {
            throw new error_middleware_1.ApiError("User already exists with this email", bookmarked_types_1.HttpStatus.CONFLICT, bookmarked_types_1.ErrorCodes.DUPLICATE_RESOURCE);
        }
        const hashedPassword = await (0, password_1.hashPassword)(password);
        const user = new User_1.UserModel({
            email,
            password: hashedPassword,
            firstName,
            lastName,
        });
        await user.save();
        const token = (0, jwt_1.generateToken)({
            userId: user._id.toString(),
            email: user.email,
        });
        user.lastLogin = new Date();
        await user.save();
        return {
            user: user.toSafeObject(),
            token,
            expiresIn: "24h",
        };
    }
    async loginUser(data) {
        const { email, password } = data;
        const user = await User_1.UserModel.findOne({ email: email.toLowerCase() }).select("+password");
        if (!user) {
            throw new error_middleware_1.ApiError("Invalid email or password", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
        }
        if (!user.isActive) {
            throw new error_middleware_1.ApiError("Account is deactivated", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
        }
        const isPasswordValid = await (0, password_1.comparePassword)(password, user.password);
        if (!isPasswordValid) {
            throw new error_middleware_1.ApiError("Invalid email or password", bookmarked_types_1.HttpStatus.UNAUTHORIZED, bookmarked_types_1.ErrorCodes.AUTHENTICATION_ERROR);
        }
        const token = (0, jwt_1.generateToken)({
            userId: user._id.toString(),
            email: user.email,
        });
        user.lastLogin = new Date();
        await user.save();
        return {
            user: user.toSafeObject(),
            token,
            expiresIn: "24h",
        };
    }
}
exports.AuthService = AuthService;
exports.authService = new AuthService();
//# sourceMappingURL=auth.service.js.map